/**
 * Coordinador de conversación fluida
 * Integra la captura de audio y la comunicación con Socket.IO
 */

import { FluidConversationService } from './FluidConversationService';
import { AudioCaptureService } from './AudioCaptureService';
import type {
  ConversationState,
  ConversationCallbacks,
  FluidConversationContextValue,
  ConversationMetrics,
  TranscriptionEvent,
  LoadingEvent,
  ReplyEvent,
  ConnectionState,
  AudioState
} from '../models/fluidConversation';

import {
  AUDIO_DEFAULTS,
  CONNECTION_DEFAULTS
} from '../models/fluidConversation';

/**
 * Coordinador principal para la conversación fluida
 * Maneja la integración entre captura de audio y comunicación Socket.IO
 */
export class FluidConversationCoordinator {
  private serviceName = 'FluidConversationCoordinator';
  private conversationService: FluidConversationService;
  private audioCaptureService: AudioCaptureService;
  private callbacks: ConversationCallbacks = {};
  private isInitialized = false;

  constructor() {
    // Crear servicios con configuración desde variables de entorno
    this.conversationService = new FluidConversationService({
      socketUrl: import.meta.env.VITE_FLUID_VOICE_API_URL || '',
      apiKey: import.meta.env.VITE_FLUID_VOICE_API_KEY || '',
      audioConfig: {
        sampleRate: AUDIO_DEFAULTS.SAMPLE_RATE,
        channelCount: AUDIO_DEFAULTS.CHANNEL_COUNT,
        sampleSize: AUDIO_DEFAULTS.SAMPLE_SIZE,
        chunkSize: AUDIO_DEFAULTS.CHUNK_SIZE,
        bufferSize: AUDIO_DEFAULTS.BUFFER_SIZE,
      },
      reconnectionAttempts: CONNECTION_DEFAULTS.RECONNECTION_ATTEMPTS,
      reconnectionDelay: CONNECTION_DEFAULTS.RECONNECTION_DELAY,
      inactivityTimeout: CONNECTION_DEFAULTS.INACTIVITY_TIMEOUT,
      debug: import.meta.env.VITE_DEBUG === 'true',
    });

    this.audioCaptureService = new AudioCaptureService();
    this.setupServiceCallbacks();
  }

  // ========== CONFIGURACIÓN ==========
  public setCallbacks(callbacks: ConversationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  private setupServiceCallbacks(): void {
    // Callbacks del servicio de conversación
    this.conversationService.setCallbacks({
      onTranscription: (event: TranscriptionEvent) => {
        this.callbacks.onTranscription?.(event);
      },
      onLoading: (event: LoadingEvent) => {
        this.callbacks.onLoading?.(event);
      },
      onReply: (event: ReplyEvent) => {
        this.callbacks.onReply?.(event);
      },
      onConnectionChange: (state: ConnectionState) => {
        this.callbacks.onConnectionChange?.(state);
      },
      onAudioStateChange: (state: AudioState) => {
        this.callbacks.onAudioStateChange?.(state);
      },
      onError: (error: Error) => {
        console.error(`❌ [${this.serviceName}] Error en conversación:`, error);
        this.callbacks.onError?.(error);
      },
      onInactivity: () => {
        this.callbacks.onInactivity?.();
      },
    });

    // Callbacks del servicio de captura de audio
    this.audioCaptureService.setCallbacks({
      onAudioData: (audioData: ArrayBuffer) => {
        // Enviar datos de audio al servicio de conversación
        this.conversationService.sendAudioChunk(audioData);
      },
      onMicLevelChange: (_level: number) => {
        // El nivel del micrófono se puede usar para UI feedback
        // Se podría agregar a los callbacks si es necesario
      },
      onStateChange: (micState) => {
        // Actualizar estado del micrófono
        console.log(`🎤 [${this.serviceName}] Estado del micrófono:`, micState);
      },
      onError: (error: Error) => {
        console.error(`❌ [${this.serviceName}] Error en captura de audio:`, error);
        this.callbacks.onError?.(error);
      },
    });
  }

  // ========== INICIALIZACIÓN ==========
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn(`⚠️ [${this.serviceName}] Ya está inicializado`);
      return;
    }

    try {
      console.log(`🚀 [${this.serviceName}] Inicializando servicios...`);

      // Inicializar captura de audio
      await this.audioCaptureService.initialize();
      console.log(`✅ [${this.serviceName}] Captura de audio inicializada`);

      // Conectar al servicio de conversación
      await this.conversationService.connect();
      console.log(`✅ [${this.serviceName}] Servicio de conversación conectado`);

      this.isInitialized = true;
      console.log(`🎉 [${this.serviceName}] Inicialización completada`);

    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error en inicialización:`, error);
      this.callbacks.onError?.(error instanceof Error ? error : new Error('Error de inicialización'));
      throw error;
    }
  }

  // ========== CONTROL DE CONVERSACIÓN ==========
  public async startConversation(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Coordinador no inicializado');
    }

    try {
      console.log(`🎙️ [${this.serviceName}] Iniciando conversación...`);

      // Iniciar captura de audio
      await this.audioCaptureService.startCapture();
      console.log(`✅ [${this.serviceName}] Captura de audio iniciada`);

    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error iniciando conversación:`, error);
      this.callbacks.onError?.(error instanceof Error ? error : new Error('Error iniciando conversación'));
      throw error;
    }
  }

  public async stopConversation(): Promise<void> {
    try {
      console.log(`🔇 [${this.serviceName}] Deteniendo conversación...`);

      // Detener captura de audio
      await this.audioCaptureService.stopCapture();
      console.log(`✅ [${this.serviceName}] Conversación detenida`);

    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error deteniendo conversación:`, error);
    }
  }

  public pauseConversation(): void {
    this.audioCaptureService.stopCapture();
  }

  public resumeConversation(): void {
    this.audioCaptureService.startCapture();
  }

  // ========== CONTROLES DE AUDIO ==========
  public muteSound(): void {
    this.conversationService.muteSound();
  }

  public unmuteSound(): void {
    this.conversationService.unmuteSound();
  }

  public toggleMute(): boolean {
    const currentState = this.conversationService.getState();
    if (currentState.isSoundMuted) {
      this.unmuteSound();
      return false;
    } else {
      this.muteSound();
      return true;
    }
  }

  // ========== INFORMACIÓN DE ESTADO ==========
  public getState(): ConversationState {
    return this.conversationService.getState();
  }

  public getMetrics(): ConversationMetrics {
    return this.conversationService.getMetrics();
  }

  public isConnected(): boolean {
    return this.conversationService.isConnected();
  }

  public isListening(): boolean {
    return this.audioCaptureService.isActive();
  }

  public isSpeaking(): boolean {
    return this.conversationService.isSpeaking();
  }

  public canSpeak(): boolean {
    return this.isConnected() && this.isInitialized;
  }

  public getMicrophoneLevel(): number {
    return this.audioCaptureService.getCurrentLevel();
  }

  // ========== VALIDACIÓN ==========
  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!import.meta.env.VITE_FLUID_VOICE_API_URL) {
      errors.push('VITE_FLUID_VOICE_API_URL no configurada');
    }

    if (!import.meta.env.VITE_FLUID_VOICE_API_KEY) {
      errors.push('VITE_FLUID_VOICE_API_KEY no configurada');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // ========== CLEANUP ==========
  public async destroy(): Promise<void> {
    try {
      console.log(`🧹 [${this.serviceName}] Limpiando recursos...`);

      await this.stopConversation();
      await this.audioCaptureService.destroy();
      this.conversationService.destroy();

      this.callbacks = {};
      this.isInitialized = false;

      console.log(`✅ [${this.serviceName}] Recursos liberados`);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error en cleanup:`, error);
    }
  }

  // ========== CONTEXT VALUE ==========
  public getContextValue(): FluidConversationContextValue {
    const state = this.getState();
    const metrics = this.getMetrics();

    return {
      // Estado
      state,
      metrics,

      // Acciones principales
      startConversation: () => this.startConversation(),
      stopConversation: () => this.stopConversation(),
      pauseConversation: () => this.pauseConversation(),
      resumeConversation: () => this.resumeConversation(),

      // Controles de audio
      muteSound: () => this.muteSound(),
      unmuteSound: () => this.unmuteSound(),
      toggleMute: () => this.toggleMute(),

      // Información
      isConnected: this.isConnected(),
      isListening: this.isListening(),
      isSpeaking: this.isSpeaking(),
      canSpeak: this.canSpeak(),
    };
  }
}

// ========== SINGLETON EXPORT ==========
export const fluidConversationCoordinator = new FluidConversationCoordinator();
