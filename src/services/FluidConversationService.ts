/**
 * Servicio de conversación fluida con IA usando Socket.IO
 * Basado en la implementación de referencia para comunicación en tiempo real
 */

import { io, Socket } from 'socket.io-client';
import type {
  ConversationState,
  ConversationCallbacks,
  FluidConversationServiceConfig,
  TranscriptionEvent,
  LoadingEvent,
  ReplyEvent,
  ProcessedAIResponse,
  AIResponseType,
  ConnectionState,

  ConversationMetrics
} from '../models/fluidConversation';

import {
  AUDIO_DEFAULTS,
  CONNECTION_DEFAULTS,
  RESPONSE_PATTERNS
} from '../models/fluidConversation';

/**
 * Servicio principal para manejar conversación fluida con IA
 */
export class FluidConversationService {
  private serviceName = 'FluidConversationService';
  private socket: Socket | null = null;
  private config: FluidConversationServiceConfig;
  private callbacks: ConversationCallbacks = {};
  private state: ConversationState;
  private metrics: ConversationMetrics;
  private inactivityTimer: NodeJS.Timeout | null = null;

  constructor(config: FluidConversationServiceConfig) {
    this.config = config;
    this.state = this.createInitialState();
    this.metrics = this.createInitialMetrics();
  }

  // ========== INICIALIZACIÓN ==========
  private createInitialState(): ConversationState {
    return {
      isActive: false,
      connectionState: 'disconnected',
      audioState: 'idle',
      microphoneState: {
        isActive: false,
        level: 0,
        isPermissionGranted: false,
        errorMessage: null,
      },
      currentTranscription: '',
      lastReply: '',
      isAILoading: false,
      isSoundMuted: false,
      isUserInactive: false,
      errorMessage: null,
    };
  }

  private createInitialMetrics(): ConversationMetrics {
    return {
      totalMessages: 0,
      totalAudioTime: 0,
      averageResponseTime: 0,
      connectionUptime: 0,
      errorsCount: 0,
      lastActivity: Date.now(),
    };
  }

  // ========== CONFIGURACIÓN ==========
  public setCallbacks(callbacks: ConversationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  public updateConfig(newConfig: Partial<FluidConversationServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // ========== CONEXIÓN ==========
  public async connect(): Promise<void> {
    if (this.socket?.connected) {
      console.warn(`⚠️ [${this.serviceName}] Ya conectado`);
      return;
    }

    try {
      this.updateConnectionState('connecting');

      this.socket = io(this.config.socketUrl, {
        auth: {
          apiKey: this.config.apiKey,
        },
        transports: ['websocket', 'polling'],
        timeout: CONNECTION_DEFAULTS.SOCKET_TIMEOUT,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
      });

      this.setupSocketListeners();

      // Esperar conexión
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout en conexión'));
        }, CONNECTION_DEFAULTS.SOCKET_TIMEOUT);

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      this.updateConnectionState('connected');
      console.log(`✅ [${this.serviceName}] Conectado exitosamente`);

    } catch (error) {
      this.updateConnectionState('error');
      const errorMessage = error instanceof Error ? error.message : 'Error de conexión';
      this.updateState({ errorMessage });
      this.callbacks.onError?.(error instanceof Error ? error : new Error(errorMessage));
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.updateConnectionState('disconnected');
    this.clearInactivityTimer();
    console.log(`🔌 [${this.serviceName}] Desconectado`);
  }

  // ========== SETUP DE LISTENERS ==========
  private setupSocketListeners(): void {
    if (!this.socket) return;

    // Eventos de conexión
    this.socket.on('connect', () => {
      this.updateConnectionState('connected');
      this.metrics.connectionUptime = Date.now();
    });

    this.socket.on('disconnect', () => {
      this.updateConnectionState('disconnected');
    });

    this.socket.on('reconnect', () => {
      this.updateConnectionState('connected');
    });

    this.socket.on('reconnecting', () => {
      this.updateConnectionState('reconnecting');
    });

    // Eventos de conversación
    this.socket.on('transcription', (transcription: string) => {
      this.handleTranscription(transcription);
    });

    this.socket.on('loading', () => {
      this.handleLoading();
    });

    this.socket.on('reply', (data: { text: string; audioUrl?: string }) => {
      this.handleReply(data);
    });

    // Manejo de errores
    this.socket.on('error', (error: any) => {
      this.handleError(error);
    });
  }

  // ========== MANEJO DE EVENTOS ==========
  private handleTranscription(transcription: string): void {
    this.clearInactivityTimer();

    const event: TranscriptionEvent = {
      text: transcription,
      timestamp: Date.now(),
      isFinal: true,
    };

    this.updateState({
      currentTranscription: transcription,
      audioState: 'processing',
    });

    this.metrics.lastActivity = Date.now();
    this.callbacks.onTranscription?.(event);
  }

  private handleLoading(): void {
    const event: LoadingEvent = {
      timestamp: Date.now(),
    };

    this.updateState({
      isAILoading: true,
      audioState: 'processing',
    });

    this.metrics.totalMessages++;
    this.callbacks.onLoading?.(event);
  }

  private handleReply(data: { text: string; audioUrl?: string }): void {
    const processedResponse = this.processAIResponse(data.text, data.audioUrl);

    const event: ReplyEvent = {
      text: processedResponse.text,
      audioUrl: processedResponse.audioUrl,
      type: processedResponse.type,
      timestamp: Date.now(),
      metadata: processedResponse.metadata,
    };

    this.updateState({
      lastReply: processedResponse.text,
      isAILoading: false,
      audioState: processedResponse.audioUrl ? 'speaking' : 'idle',
    });

    // Manejar acciones especiales
    this.handleSpecialActions(processedResponse);

    this.callbacks.onReply?.(event);
    this.startInactivityTimer();
  }

  private handleError(error: any): void {
    this.metrics.errorsCount++;
    this.updateState({
      errorMessage: error.message || 'Error desconocido',
      audioState: 'error',
    });

    this.callbacks.onError?.(error instanceof Error ? error : new Error(error.message));
  }

  // ========== PROCESAMIENTO DE RESPUESTAS ==========
  private processAIResponse(text: string, audioUrl?: string): ProcessedAIResponse {
    let type: AIResponseType = 'normal';
    let processedText = text;
    let shouldExit = false;
    let shouldPause = false;
    let command: string | undefined;
    let recommendations: any[] | undefined;

    // Detectar tipo de respuesta usando patrones
    if (RESPONSE_PATTERNS.EXIT.test(text)) {
      type = 'exit';
      shouldExit = true;
      processedText = 'Cerrando...';
    } else if (RESPONSE_PATTERNS.PAUSE.test(text)) {
      type = 'pause';
      shouldPause = true;
      processedText = 'Estoy esperando, indica "Ok Aura, continuar" para seguir con la conversación';
    } else if (RESPONSE_PATTERNS.COMMAND.test(text)) {
      type = 'command';
      command = text.replace('[COMMAND] ', '');
      processedText = `${command}...`;
    } else if (RESPONSE_PATTERNS.NONE.test(text)) {
      type = 'none';
      processedText = 'No puedo ayudarte con eso. Te puedo ayudar a darte una recomendación personalizada, ¿Qué te apetece ver hoy?';
    } else if (RESPONSE_PATTERNS.OK_AURA.test(text)) {
      type = 'ok_aura';
      processedText = 'Recuerda que en esta experiencia de voz no necesitas volver a indicar "Ok Aura" mientras el micrófono esté activo';
    } else if (RESPONSE_PATTERNS.JSON.test(text)) {
      type = 'json';
      try {
        recommendations = JSON.parse(text.replace('[JSON] ', ''));
        processedText = 'Cargando recomendaciones...';
      } catch (error) {
        console.error('Error parseando JSON:', error);
        type = 'error';
        processedText = 'Error procesando recomendaciones';
      }
    }

    return {
      type,
      text: processedText,
      audioUrl,
      shouldExit,
      shouldPause,
      command,
      recommendations,
      metadata: {
        originalText: text,
        timestamp: Date.now(),
      },
    };
  }

  private handleSpecialActions(response: ProcessedAIResponse): void {
    if (response.shouldExit) {
      // Manejar cierre de aplicación
      this.updateState({ isActive: false });
    } else if (response.shouldPause) {
      // Manejar pausa
      this.updateState({ audioState: 'paused' });
    } else if (response.command) {
      // Manejar comandos especiales
      console.log(`🎯 [${this.serviceName}] Comando: ${response.command}`);
    } else if (response.recommendations) {
      // Manejar recomendaciones
      console.log(`📋 [${this.serviceName}] Recomendaciones recibidas:`, response.recommendations);
    }
  }

  // ========== ENVÍO DE AUDIO ==========
  public sendAudioChunk(audioData: ArrayBuffer): void {
    if (!this.socket?.connected) {
      console.warn(`⚠️ [${this.serviceName}] No conectado, no se puede enviar audio`);
      return;
    }

    this.socket.emit('audio', audioData);
    this.metrics.lastActivity = Date.now();
  }

  // ========== CONTROLES DE AUDIO ==========
  public muteSound(): void {
    if (!this.socket?.connected) return;

    this.socket.emit('mute-sound');
    this.updateState({ isSoundMuted: true });
  }

  public unmuteSound(): void {
    if (!this.socket?.connected) return;

    this.socket.emit('unmute-sound');
    this.updateState({ isSoundMuted: false });
  }

  // ========== GESTIÓN DE INACTIVIDAD ==========
  private startInactivityTimer(): void {
    this.clearInactivityTimer();

    this.inactivityTimer = setTimeout(() => {
      this.updateState({ isUserInactive: true });
      this.callbacks.onInactivity?.();
    }, this.config.inactivityTimeout);
  }

  private clearInactivityTimer(): void {
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }
    this.updateState({ isUserInactive: false });
  }

  // ========== GESTIÓN DE ESTADO ==========
  private updateState(updates: Partial<ConversationState>): void {
    this.state = { ...this.state, ...updates };
  }

  private updateConnectionState(connectionState: ConnectionState): void {
    this.state.connectionState = connectionState;
    this.callbacks.onConnectionChange?.(connectionState);
  }



  // ========== GETTERS ==========
  public getState(): ConversationState {
    return { ...this.state };
  }

  public getMetrics(): ConversationMetrics {
    return { ...this.metrics };
  }

  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  public isListening(): boolean {
    return this.state.audioState === 'listening';
  }

  public isSpeaking(): boolean {
    return this.state.audioState === 'speaking';
  }

  // ========== CLEANUP ==========
  public destroy(): void {
    this.clearInactivityTimer();
    this.disconnect();
    this.callbacks = {};
  }
}

// ========== SINGLETON EXPORT ==========
export const fluidConversationService = new FluidConversationService({
  socketUrl: import.meta.env.VITE_FLUID_VOICE_API_URL || '',
  apiKey: import.meta.env.VITE_FLUID_VOICE_API_KEY || '',
  audioConfig: {
    sampleRate: AUDIO_DEFAULTS.SAMPLE_RATE,
    channelCount: AUDIO_DEFAULTS.CHANNEL_COUNT,
    sampleSize: AUDIO_DEFAULTS.SAMPLE_SIZE,
    chunkSize: AUDIO_DEFAULTS.CHUNK_SIZE,
    bufferSize: AUDIO_DEFAULTS.BUFFER_SIZE,
  },
  reconnectionAttempts: CONNECTION_DEFAULTS.RECONNECTION_ATTEMPTS,
  reconnectionDelay: CONNECTION_DEFAULTS.RECONNECTION_DELAY,
  inactivityTimeout: CONNECTION_DEFAULTS.INACTIVITY_TIMEOUT,
  debug: import.meta.env.VITE_DEBUG === 'true',
});
