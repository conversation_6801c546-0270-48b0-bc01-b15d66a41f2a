/**
 * Servicio de captura y procesamiento de audio para conversación fluida
 * Maneja la captura del micrófono, procesamiento con AudioWorklet y monitoreo de nivel
 */

import type {
  AudioConfig,
  MicrophoneState
} from '../models/fluidConversation';

import { AUDIO_DEFAULTS } from '../models/fluidConversation';

export interface AudioCaptureCallbacks {
  onAudioData?: (audioData: ArrayBuffer) => void;
  onMicLevelChange?: (level: number) => void;
  onStateChange?: (state: MicrophoneState) => void;
  onError?: (error: Error) => void;
}

/**
 * Servicio para capturar y procesar audio del micrófono
 */
export class AudioCaptureService {
  private serviceName = 'AudioCaptureService';
  private audioContext: AudioContext | null = null;
  private mediaStream: MediaStream | null = null;
  private audioWorkletNode: AudioWorkletNode | null = null;
  private analyserNode: AnalyserNode | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;

  private config: AudioConfig;
  private callbacks: AudioCaptureCallbacks = {};
  private state: MicrophoneState;
  private isCapturing = false;
  private levelMonitoringInterval: NodeJS.Timeout | null = null;

  constructor(config?: Partial<AudioConfig>) {
    this.config = {
      sampleRate: AUDIO_DEFAULTS.SAMPLE_RATE,
      channelCount: AUDIO_DEFAULTS.CHANNEL_COUNT,
      sampleSize: AUDIO_DEFAULTS.SAMPLE_SIZE,
      chunkSize: AUDIO_DEFAULTS.CHUNK_SIZE,
      bufferSize: AUDIO_DEFAULTS.BUFFER_SIZE,
      ...config,
    };

    this.state = {
      isActive: false,
      level: 0,
      isPermissionGranted: false,
      errorMessage: null,
    };
  }

  // ========== CONFIGURACIÓN ==========
  public setCallbacks(callbacks: AudioCaptureCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  public updateConfig(newConfig: Partial<AudioConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // ========== INICIALIZACIÓN ==========
  public async initialize(): Promise<void> {
    try {
      await this.requestMicrophonePermission();
      await this.setupAudioContext();
      await this.setupAudioWorklet();

      console.log(`✅ [${this.serviceName}] Inicializado correctamente`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error de inicialización';
      this.updateState({ errorMessage });
      this.callbacks.onError?.(error instanceof Error ? error : new Error(errorMessage));
      throw error;
    }
  }

  private async requestMicrophonePermission(): Promise<void> {
    try {
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: this.config.channelCount,
          sampleRate: this.config.sampleRate,
          sampleSize: this.config.sampleSize,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      });

      this.updateState({
        isPermissionGranted: true,
        errorMessage: null
      });

      console.log(`🎤 [${this.serviceName}] Permiso de micrófono concedido`);
    } catch (error) {
      this.updateState({
        isPermissionGranted: false,
        errorMessage: 'Permiso de micrófono denegado'
      });
      throw new Error('No se pudo acceder al micrófono');
    }
  }

  private async setupAudioContext(): Promise<void> {
    if (!this.mediaStream) {
      throw new Error('MediaStream no disponible');
    }

    this.audioContext = new AudioContext({
      sampleRate: this.config.sampleRate,
    });

    // Crear nodos de audio
    this.sourceNode = this.audioContext.createMediaStreamSource(this.mediaStream);
    this.analyserNode = this.audioContext.createAnalyser();

    // Configurar analizador para monitoreo de nivel
    this.analyserNode.fftSize = 256;
    this.analyserNode.smoothingTimeConstant = 0.8;

    console.log(`🔊 [${this.serviceName}] AudioContext configurado - Sample Rate: ${this.audioContext.sampleRate}Hz`);
  }

  private async setupAudioWorklet(): Promise<void> {
    if (!this.audioContext) {
      throw new Error('AudioContext no disponible');
    }

    try {
      // Cargar el AudioWorklet
      await this.audioContext.audioWorklet.addModule('/audio-processor.js');

      // Crear el nodo del AudioWorklet
      this.audioWorkletNode = new AudioWorkletNode(this.audioContext, 'audio-processor');

      // Configurar el manejo de mensajes del AudioWorklet
      this.audioWorkletNode.port.onmessage = (event) => {
        this.handleAudioData(event.data);
      };

      console.log(`⚙️ [${this.serviceName}] AudioWorklet configurado`);
    } catch (error) {
      throw new Error(`Error configurando AudioWorklet: ${error}`);
    }
  }

  // ========== CAPTURA DE AUDIO ==========
  public async startCapture(): Promise<void> {
    if (this.isCapturing) {
      console.warn(`⚠️ [${this.serviceName}] Ya está capturando audio`);
      return;
    }

    if (!this.audioContext || !this.sourceNode || !this.audioWorkletNode || !this.analyserNode) {
      throw new Error('Servicio no inicializado correctamente');
    }

    try {
      // Conectar los nodos de audio
      this.sourceNode.connect(this.audioWorkletNode);
      this.sourceNode.connect(this.analyserNode);

      this.isCapturing = true;
      this.updateState({ isActive: true });

      // Iniciar monitoreo de nivel
      this.startLevelMonitoring();

      console.log(`🎙️ [${this.serviceName}] Captura de audio iniciada`);
    } catch (error) {
      this.updateState({
        errorMessage: 'Error iniciando captura de audio',
        isActive: false
      });
      throw error;
    }
  }

  public async stopCapture(): Promise<void> {
    if (!this.isCapturing) {
      return;
    }

    try {
      // Desconectar nodos
      if (this.sourceNode && this.audioWorkletNode) {
        this.sourceNode.disconnect(this.audioWorkletNode);
      }
      if (this.sourceNode && this.analyserNode) {
        this.sourceNode.disconnect(this.analyserNode);
      }

      this.isCapturing = false;
      this.updateState({ isActive: false, level: 0 });

      // Detener monitoreo de nivel
      this.stopLevelMonitoring();

      console.log(`🔇 [${this.serviceName}] Captura de audio detenida`);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error deteniendo captura:`, error);
    }
  }

  // ========== PROCESAMIENTO DE AUDIO ==========
  private handleAudioData(audioSamples: Float32Array): void {
    if (!this.isCapturing) return;

    try {
      // Convertir Float32Array a Int16Array
      const int16Buffer = new Int16Array(audioSamples.length);

      for (let i = 0; i < audioSamples.length; i++) {
        // Convertir de float (-1 a 1) a int16 (-32768 a 32767)
        int16Buffer[i] = Math.max(-32768, Math.min(32767, audioSamples[i] * 32768));
      }

      // Enviar como ArrayBuffer
      this.callbacks.onAudioData?.(int16Buffer.buffer);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error procesando audio:`, error);
      this.callbacks.onError?.(error instanceof Error ? error : new Error('Error procesando audio'));
    }
  }

  // ========== MONITOREO DE NIVEL ==========
  private startLevelMonitoring(): void {
    if (!this.analyserNode) return;

    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);

    this.levelMonitoringInterval = setInterval(() => {
      if (!this.analyserNode || !this.isCapturing) return;

      this.analyserNode.getByteFrequencyData(dataArray);

      // Calcular nivel promedio
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const averageLevel = sum / dataArray.length;

      // Normalizar a 0-100
      const normalizedLevel = Math.round((averageLevel / 255) * 100);

      this.updateState({ level: normalizedLevel });
      this.callbacks.onMicLevelChange?.(normalizedLevel);
    }, 100); // Actualizar cada 100ms
  }

  private stopLevelMonitoring(): void {
    if (this.levelMonitoringInterval) {
      clearInterval(this.levelMonitoringInterval);
      this.levelMonitoringInterval = null;
    }
  }

  // ========== GESTIÓN DE ESTADO ==========
  private updateState(updates: Partial<MicrophoneState>): void {
    this.state = { ...this.state, ...updates };
    this.callbacks.onStateChange?.(this.state);
  }

  // ========== GETTERS ==========
  public getState(): MicrophoneState {
    return { ...this.state };
  }

  public isInitialized(): boolean {
    return !!(this.audioContext && this.mediaStream && this.audioWorkletNode);
  }

  public isActive(): boolean {
    return this.isCapturing;
  }

  public getCurrentLevel(): number {
    return this.state.level;
  }

  // ========== CLEANUP ==========
  public async destroy(): Promise<void> {
    try {
      await this.stopCapture();
      this.stopLevelMonitoring();

      // Cerrar MediaStream
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => {
          track.stop();
        });
        this.mediaStream = null;
      }

      // Cerrar AudioContext
      if (this.audioContext && this.audioContext.state !== 'closed') {
        await this.audioContext.close();
        this.audioContext = null;
      }

      // Limpiar referencias
      this.sourceNode = null;
      this.audioWorkletNode = null;
      this.analyserNode = null;
      this.callbacks = {};

      this.updateState({
        isActive: false,
        level: 0,
        isPermissionGranted: false,
        errorMessage: null,
      });

      console.log(`🧹 [${this.serviceName}] Recursos liberados`);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error en cleanup:`, error);
    }
  }
}
