/**
 * Modelos y tipos para la conversación fluida con IA
 * Basado en la implementación de Socket.IO para comunicación en tiempo real
 */

// ========== TIPOS DE EVENTOS SOCKET.IO ==========
export type SocketEventType = 
  | "audio"
  | "transcription" 
  | "loading"
  | "reply"
  | "mute-sound"
  | "unmute-sound"
  | "connect"
  | "disconnect"
  | "error";

// ========== ESTADOS DE CONEXIÓN ==========
export type ConnectionState = 
  | "disconnected"
  | "connecting" 
  | "connected"
  | "reconnecting"
  | "error";

// ========== ESTADOS DE AUDIO ==========
export type AudioState = 
  | "idle"
  | "listening"
  | "processing"
  | "speaking"
  | "paused"
  | "error";

// ========== TIPOS DE RESPUESTA DE LA IA ==========
export type AIResponseType = 
  | "normal"
  | "exit"
  | "pause"
  | "command"
  | "none"
  | "ok_aura"
  | "json"
  | "error";

// ========== INTERFACES DE EVENTOS ==========
export interface TranscriptionEvent {
  text: string;
  confidence?: number;
  isFinal?: boolean;
  timestamp: number;
}

export interface LoadingEvent {
  message?: string;
  timestamp: number;
}

export interface ReplyEvent {
  text: string;
  audioUrl?: string;
  type: AIResponseType;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface AudioChunk {
  data: ArrayBuffer;
  timestamp: number;
  sequenceNumber: number;
}

// ========== CONFIGURACIÓN DE AUDIO ==========
export interface AudioConfig {
  sampleRate: number;
  channelCount: number;
  sampleSize: number;
  chunkSize: number;
  bufferSize: number;
}

// ========== ESTADO DEL MICRÓFONO ==========
export interface MicrophoneState {
  isActive: boolean;
  level: number;
  isPermissionGranted: boolean;
  errorMessage: string | null;
}

// ========== ESTADO DE LA CONVERSACIÓN ==========
export interface ConversationState {
  isActive: boolean;
  connectionState: ConnectionState;
  audioState: AudioState;
  microphoneState: MicrophoneState;
  currentTranscription: string;
  lastReply: string;
  isAILoading: boolean;
  isSoundMuted: boolean;
  isUserInactive: boolean;
  errorMessage: string | null;
}

// ========== CONFIGURACIÓN DEL SERVICIO ==========
export interface FluidConversationServiceConfig {
  socketUrl: string;
  apiKey: string;
  audioConfig: AudioConfig;
  reconnectionAttempts: number;
  reconnectionDelay: number;
  inactivityTimeout: number;
  debug: boolean;
}

// ========== CALLBACKS Y EVENTOS ==========
export interface ConversationCallbacks {
  onTranscription?: (event: TranscriptionEvent) => void;
  onLoading?: (event: LoadingEvent) => void;
  onReply?: (event: ReplyEvent) => void;
  onConnectionChange?: (state: ConnectionState) => void;
  onAudioStateChange?: (state: AudioState) => void;
  onError?: (error: Error) => void;
  onInactivity?: () => void;
}

// ========== MÉTRICAS Y ESTADÍSTICAS ==========
export interface ConversationMetrics {
  totalMessages: number;
  totalAudioTime: number;
  averageResponseTime: number;
  connectionUptime: number;
  errorsCount: number;
  lastActivity: number;
}

// ========== RESPUESTA PROCESADA DE LA IA ==========
export interface ProcessedAIResponse {
  type: AIResponseType;
  text: string;
  audioUrl?: string;
  shouldExit?: boolean;
  shouldPause?: boolean;
  command?: string;
  recommendations?: any[];
  metadata?: Record<string, any>;
}

// ========== CONFIGURACIÓN DE SOCKET.IO ==========
export interface SocketConfig {
  url: string;
  options: {
    auth: {
      apiKey: string;
    };
    transports: string[];
    timeout: number;
    reconnectionAttempts: number;
    reconnectionDelay: number;
  };
}

// ========== EVENTOS DEL CONTEXTO ==========
export interface FluidConversationContextValue {
  // Estado
  state: ConversationState;
  metrics: ConversationMetrics;
  
  // Acciones principales
  startConversation: () => Promise<void>;
  stopConversation: () => Promise<void>;
  pauseConversation: () => void;
  resumeConversation: () => void;
  
  // Controles de audio
  muteSound: () => void;
  unmuteSound: () => void;
  toggleMute: () => boolean;
  
  // Información
  isConnected: boolean;
  isListening: boolean;
  isSpeaking: boolean;
  canSpeak: boolean;
}

// ========== TIPOS DE UTILIDAD ==========
export type ConversationEventHandler<T = any> = (data: T) => void;
export type ConversationErrorHandler = (error: Error) => void;
export type ConversationStateUpdater = (state: Partial<ConversationState>) => void;

// ========== CONSTANTES ==========
export const AUDIO_DEFAULTS = {
  SAMPLE_RATE: 16000,
  CHANNEL_COUNT: 1,
  SAMPLE_SIZE: 16,
  CHUNK_SIZE: 1600, // 100ms a 16kHz
  BUFFER_SIZE: 4096,
} as const;

export const CONNECTION_DEFAULTS = {
  RECONNECTION_ATTEMPTS: 5,
  RECONNECTION_DELAY: 1000,
  SOCKET_TIMEOUT: 10000,
  INACTIVITY_TIMEOUT: 20000,
} as const;

export const RESPONSE_PATTERNS = {
  EXIT: /^\[EXIT\]/,
  PAUSE: /^\[PAUSE\]/,
  COMMAND: /^\[COMMAND\]/,
  NONE: /^\[NONE\]/,
  OK_AURA: /^\[OK AURA\]$/,
  JSON: /^\[JSON\]/,
} as const;
