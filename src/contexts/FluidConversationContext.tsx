/**
 * Contexto de conversación fluida con IA
 * Proporciona funcionalidad de conversación en tiempo real usando Socket.IO
 */

import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
} from 'react';
import { fluidConversationCoordinator } from '../services/FluidConversationCoordinator';
import type {
  ConversationState,
  ConversationMetrics,
  FluidConversationContextValue,
  TranscriptionEvent,
  LoadingEvent,
  ReplyEvent,
  ConnectionState,
  AudioState,
} from '../models/fluidConversation';

// ========== CONTEXTO ==========
const FluidConversationContext = createContext<FluidConversationContextValue | null>(null);

// ========== HOOK ==========
export const useFluidConversation = (): FluidConversationContextValue => {
  const context = useContext(FluidConversationContext);
  if (!context) {
    throw new Error('useFluidConversation debe usarse dentro de FluidConversationProvider');
  }
  return context;
};

// ========== PROVIDER ==========
interface FluidConversationProviderProps {
  children: ReactNode;
}

export const FluidConversationProvider = ({ children }: FluidConversationProviderProps) => {
  // ========== ESTADOS ==========
  const [state, setState] = useState<ConversationState>(() => ({
    isActive: false,
    connectionState: 'disconnected',
    audioState: 'idle',
    microphoneState: {
      isActive: false,
      level: 0,
      isPermissionGranted: false,
      errorMessage: null,
    },
    currentTranscription: '',
    lastReply: '',
    isAILoading: false,
    isSoundMuted: false,
    isUserInactive: false,
    errorMessage: null,
  }));

  const [metrics, setMetrics] = useState<ConversationMetrics>(() => ({
    totalMessages: 0,
    totalAudioTime: 0,
    averageResponseTime: 0,
    connectionUptime: 0,
    errorsCount: 0,
    lastActivity: Date.now(),
  }));

  const [isInitialized, setIsInitialized] = useState(false);

  // ========== CALLBACKS ==========
  const handleTranscription = useCallback((event: TranscriptionEvent) => {
    setState(prev => ({
      ...prev,
      currentTranscription: event.text,
    }));
    console.log(`📝 [FluidConversation] Transcripción: ${event.text}`);
  }, []);

  const handleLoading = useCallback((_event: LoadingEvent) => {
    setState(prev => ({
      ...prev,
      isAILoading: true,
    }));
    console.log(`⏳ [FluidConversation] IA procesando...`);
  }, []);

  const handleReply = useCallback((event: ReplyEvent) => {
    setState(prev => ({
      ...prev,
      lastReply: event.text,
      isAILoading: false,
    }));
    console.log(`🤖 [FluidConversation] Respuesta: ${event.text}`);
  }, []);

  const handleConnectionChange = useCallback((connectionState: ConnectionState) => {
    setState(prev => ({
      ...prev,
      connectionState,
    }));
    console.log(`🔗 [FluidConversation] Conexión: ${connectionState}`);
  }, []);

  const handleAudioStateChange = useCallback((audioState: AudioState) => {
    setState(prev => ({
      ...prev,
      audioState,
    }));
    console.log(`🔊 [FluidConversation] Audio: ${audioState}`);
  }, []);

  const handleError = useCallback((error: Error) => {
    setState(prev => ({
      ...prev,
      errorMessage: error.message,
    }));
    console.error(`❌ [FluidConversation] Error:`, error);
  }, []);

  const handleInactivity = useCallback(() => {
    setState(prev => ({
      ...prev,
      isUserInactive: true,
    }));
    console.log(`😴 [FluidConversation] Usuario inactivo`);
  }, []);

  // ========== INICIALIZACIÓN ==========
  useEffect(() => {
    const initializeService = async () => {
      try {
        // Configurar callbacks
        fluidConversationCoordinator.setCallbacks({
          onTranscription: handleTranscription,
          onLoading: handleLoading,
          onReply: handleReply,
          onConnectionChange: handleConnectionChange,
          onAudioStateChange: handleAudioStateChange,
          onError: handleError,
          onInactivity: handleInactivity,
        });

        // Validar configuración
        const validation = fluidConversationCoordinator.validateConfiguration();
        if (!validation.isValid) {
          console.warn(`⚠️ [FluidConversation] Configuración incompleta:`, validation.errors);
          return;
        }

        // Inicializar coordinador
        await fluidConversationCoordinator.initialize();
        setIsInitialized(true);

        console.log(`✅ [FluidConversation] Servicio inicializado`);
      } catch (error) {
        console.error(`❌ [FluidConversation] Error en inicialización:`, error);
        handleError(error instanceof Error ? error : new Error('Error de inicialización'));
      }
    };

    initializeService();

    // Cleanup al desmontar
    return () => {
      fluidConversationCoordinator.destroy();
    };
  }, [
    handleTranscription,
    handleLoading,
    handleReply,
    handleConnectionChange,
    handleAudioStateChange,
    handleError,
    handleInactivity,
  ]);

  // ========== ACTUALIZACIÓN DE ESTADO ==========
  useEffect(() => {
    if (!isInitialized) return;

    const updateInterval = setInterval(() => {
      const currentState = fluidConversationCoordinator.getState();
      const currentMetrics = fluidConversationCoordinator.getMetrics();

      setState(currentState);
      setMetrics(currentMetrics);
    }, 1000); // Actualizar cada segundo

    return () => clearInterval(updateInterval);
  }, [isInitialized]);

  // ========== ACCIONES ==========
  const startConversation = useCallback(async (): Promise<void> => {
    if (!isInitialized) {
      throw new Error('Servicio no inicializado');
    }

    try {
      await fluidConversationCoordinator.startConversation();
      setState(prev => ({ ...prev, isActive: true }));
    } catch (error) {
      handleError(error instanceof Error ? error : new Error('Error iniciando conversación'));
      throw error;
    }
  }, [isInitialized, handleError]);

  const stopConversation = useCallback(async (): Promise<void> => {
    try {
      await fluidConversationCoordinator.stopConversation();
      setState(prev => ({ ...prev, isActive: false }));
    } catch (error) {
      handleError(error instanceof Error ? error : new Error('Error deteniendo conversación'));
      throw error;
    }
  }, [handleError]);

  const pauseConversation = useCallback((): void => {
    fluidConversationCoordinator.pauseConversation();
    setState(prev => ({ ...prev, audioState: 'paused' }));
  }, []);

  const resumeConversation = useCallback((): void => {
    fluidConversationCoordinator.resumeConversation();
    setState(prev => ({ ...prev, audioState: 'listening' }));
  }, []);

  const muteSound = useCallback((): void => {
    fluidConversationCoordinator.muteSound();
    setState(prev => ({ ...prev, isSoundMuted: true }));
  }, []);

  const unmuteSound = useCallback((): void => {
    fluidConversationCoordinator.unmuteSound();
    setState(prev => ({ ...prev, isSoundMuted: false }));
  }, []);

  const toggleMute = useCallback((): boolean => {
    const isMuted = fluidConversationCoordinator.toggleMute();
    setState(prev => ({ ...prev, isSoundMuted: isMuted }));
    return isMuted;
  }, []);

  // ========== PROPIEDADES COMPUTADAS ==========
  const isConnected = fluidConversationCoordinator.isConnected();
  const isListening = fluidConversationCoordinator.isListening();
  const isSpeaking = fluidConversationCoordinator.isSpeaking();
  const canSpeak = fluidConversationCoordinator.canSpeak();

  // ========== VALOR DEL CONTEXTO ==========
  const contextValue: FluidConversationContextValue = {
    // Estado
    state,
    metrics,

    // Acciones principales
    startConversation,
    stopConversation,
    pauseConversation,
    resumeConversation,

    // Controles de audio
    muteSound,
    unmuteSound,
    toggleMute,

    // Información
    isConnected,
    isListening,
    isSpeaking,
    canSpeak,
  };

  return (
    <FluidConversationContext.Provider value={contextValue}>
      {children}
    </FluidConversationContext.Provider>
  );
};
