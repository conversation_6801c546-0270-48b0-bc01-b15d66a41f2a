import { useState, useEffect, useRef } from "react";
import { Image, Modal } from "microapps";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { useQuestionsColor } from "../../../utils/questionsColorSystem";
import IconAura from "../../Buttons/Aura/IconAura";
import "./PlayView.scss";

interface PlayViewProps {
  handleShowClues: () => void;
  handleExistGame: () => void;
  showExitPopup: boolean;
  handleConfirmExit: () => void;
  handleCancelExit: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({
  handleShowClues,
  handleExistGame,
  showExitPopup,
  handleConfirmExit,
  handleCancelExit,
}) => {
  const { session, askQuestion, askInitialMessage } = useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLivesPopUpShown, setIsLivesPopUpShown] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // Hook para obtener el color basado en preguntas restantes
  const questionsColorData = useQuestionsColor(
    session?.maxQuestions || 20,
    session?.questionCount || 0
  );
  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map(
        (msg: SessionMessage): ChatMessage => ({
          id: msg.id,
          text: msg.text,
          sender: msg.sender,
          timestamp: msg.timestamp,
        })
      );
      setMessages(chatMessages);
    }
  }, [session?.messages]);

  useEffect(() => {
    if (
      session &&
      (!session.messages || session.messages.length === 0) &&
      messages.length === 0
    ) {
      const sendInitialHola = async () => {
        try {
          setIsLoading(true);
          await askInitialMessage("Hola");
        } catch (error) {
          console.error("Error sending initial Hola:", error);
        } finally {
          setIsLoading(false);
        }
      };

      sendInitialHola();
    }
  }, [session, messages.length, askInitialMessage]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      await askQuestion(messageText);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: "Lo siento, hubo un error al procesar tu mensaje. Inténtalo de nuevo.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      <div className="chat-view">
        <div className="menu-left">
          <div className="enygma-logo">
            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <div className="icon-aura speech-indicator">
                <div className="speech-pulse">
                  <IconAura />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="chat-view-wrapper">
          <div className="chat-container">
            <div
              className={`chat-content ${
                messages.length > 0 &&
                messages[messages.length - 1].sender === "user"
                  ? "align-right"
                  : "align-left"
              }`}
            >
              <div className="chat-text body1">
                {messages.length > 0
                  ? messages[messages.length - 1].text
                  : "Espera un momento..."}
              </div>
            </div>
          </div>

          <div className="chat-input-container">
            <div className="input-wrapper">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Haz una pregunta sobre el personaje..."
                disabled={isLoading || !session}
                className="chat-input"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim() || isLoading || !session}
                className="send-button"
              >
                {isLoading ? "Enviando..." : "Enviar"}
              </button>
            </div>
          </div>
        </div>

        <div className="menu-right">
          <div
            onClick={() => setIsLivesPopUpShown((prev) => !prev)}
            className="image-button"
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/lives.png"
              alt="Vidas"
              className="book-image"
            />

            {session && (
              <p
                className={`body2 bold questions-color-text ${questionsColorData.colorClass}`}
                style={{
                  // Opcional: usar el color hex directamente si prefieres
                  color: questionsColorData.hexColor
                }}
              >
                {session?.maxQuestions! - session?.questionCount!}
              </p>
            )}
          </div>

          <div onClick={handleShowClues} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/clues.png"
              alt="Pistas"
              className="clues-image"
            />
            <p className="body2 bold">Pistas</p>
          </div>

          <div onClick={handleExistGame} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/exit.png"
              alt="Salir"
              className="exit-image"
            />
            <p className="body2 bold">Salir</p>
          </div>
        </div>
      </div>

      {showExitPopup && (
        <Modal
          title="¿Seguro que quieres salir del juego?"
          onClose={handleCancelExit}
          onCancel={handleConfirmExit}
          onConfirm={handleCancelExit}
          cancelText="Salir de todos modos"
          confirmText="Seguir jugando"
          body="Si sales ahora, vas a perder tu progreso actual. Puedes seguir jugando o salir cuando quieras."
        />
      )}

      {isLivesPopUpShown && (
        <Modal
          title="Tus preguntas restantes"
          onClose={() => setIsLivesPopUpShown((prev) => !prev)}
          onConfirm={() => setIsLivesPopUpShown((prev) => !prev)}
          confirmText="Entendido"
          body=" Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada
          vez que haces una, se descuenta del contador. Piensa bien cada
          pregunta: ¡cada una cuenta!"
        />
      )}
    </>
  );
};

export default PlayView;
