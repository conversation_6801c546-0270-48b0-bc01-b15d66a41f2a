/**
 * Interfaz de conversación fluida con IA
 * Muestra transcripción en tiempo real, estado del micrófono y controles de audio
 */

import { useState, useEffect } from 'react';
import { useFluidConversation } from '../contexts/FluidConversationContext';
import { useEnygmaGame } from '../contexts/EnygmaGameContext';
import './FluidConversationInterface.scss';

interface FluidConversationInterfaceProps {
  className?: string;
}

export const FluidConversationInterface = ({ 
  className = '' 
}: FluidConversationInterfaceProps) => {
  const fluidConversation = useFluidConversation();
  const { session, startFluidConversation, stopFluidConversation } = useEnygmaGame();
  
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ========== ESTADO DERIVADO ==========
  const { state, isConnected, isListening, isSpeaking, canSpeak } = fluidConversation;
  const isFluidMode = session?.mode === 'fluid_conversation';
  const isActive = session?.fluidConversationActive || false;

  // ========== EFECTOS ==========
  useEffect(() => {
    // Limpiar errores cuando el estado cambia
    if (isConnected && error) {
      setError(null);
    }
  }, [isConnected, error]);

  // ========== HANDLERS ==========
  const handleStartConversation = async () => {
    if (!isFluidMode) {
      setError('Modo de conversación fluida no activo');
      return;
    }

    setIsInitializing(true);
    setError(null);

    try {
      await startFluidConversation();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error iniciando conversación';
      setError(errorMessage);
      console.error('❌ Error iniciando conversación fluida:', err);
    } finally {
      setIsInitializing(false);
    }
  };

  const handleStopConversation = async () => {
    try {
      await stopFluidConversation();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deteniendo conversación';
      setError(errorMessage);
      console.error('❌ Error deteniendo conversación fluida:', err);
    }
  };

  const handleToggleMute = () => {
    try {
      fluidConversation.toggleMute();
    } catch (err) {
      console.error('❌ Error alternando mute:', err);
    }
  };

  // ========== RENDERIZADO CONDICIONAL ==========
  if (!isFluidMode) {
    return (
      <div className={`fluid-conversation-interface ${className}`}>
        <div className="fluid-conversation-interface__not-available">
          <p>Conversación fluida no disponible en este modo de juego</p>
        </div>
      </div>
    );
  }

  // ========== RENDER PRINCIPAL ==========
  return (
    <div className={`fluid-conversation-interface ${className}`}>
      {/* Header con estado de conexión */}
      <div className="fluid-conversation-interface__header">
        <div className={`connection-status connection-status--${state.connectionState}`}>
          <span className="connection-status__indicator"></span>
          <span className="connection-status__text">
            {state.connectionState === 'connected' && 'Conectado'}
            {state.connectionState === 'connecting' && 'Conectando...'}
            {state.connectionState === 'disconnected' && 'Desconectado'}
            {state.connectionState === 'reconnecting' && 'Reconectando...'}
            {state.connectionState === 'error' && 'Error de conexión'}
          </span>
        </div>
      </div>

      {/* Área de transcripción */}
      <div className="fluid-conversation-interface__transcription">
        <div className="transcription-area">
          <div className="transcription-area__current">
            <label>Transcripción en tiempo real:</label>
            <div className="transcription-text">
              {state.currentTranscription || (isListening ? 'Escuchando...' : 'No hay transcripción')}
            </div>
          </div>
          
          {state.lastReply && (
            <div className="transcription-area__reply">
              <label>Última respuesta de la IA:</label>
              <div className="reply-text">
                {state.lastReply}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Indicador de micrófono */}
      <div className="fluid-conversation-interface__microphone">
        <div className={`microphone-indicator ${isListening ? 'microphone-indicator--active' : ''}`}>
          <div className="microphone-icon">
            🎤
          </div>
          <div className="microphone-level">
            <div 
              className="microphone-level__bar"
              style={{ width: `${state.microphoneState.level}%` }}
            ></div>
          </div>
          <div className="microphone-status">
            {isListening ? 'Escuchando' : 'Inactivo'}
          </div>
        </div>
      </div>

      {/* Estados de la IA */}
      <div className="fluid-conversation-interface__ai-status">
        {state.isAILoading && (
          <div className="ai-status ai-status--loading">
            <span className="ai-status__spinner">⏳</span>
            <span>IA procesando...</span>
          </div>
        )}
        
        {isSpeaking && (
          <div className="ai-status ai-status--speaking">
            <span className="ai-status__icon">🗣️</span>
            <span>IA hablando...</span>
          </div>
        )}
      </div>

      {/* Controles */}
      <div className="fluid-conversation-interface__controls">
        {!isActive ? (
          <button
            className="control-button control-button--start"
            onClick={handleStartConversation}
            disabled={!canSpeak || isInitializing}
          >
            {isInitializing ? 'Iniciando...' : 'Iniciar Conversación'}
          </button>
        ) : (
          <div className="control-buttons">
            <button
              className="control-button control-button--stop"
              onClick={handleStopConversation}
            >
              Detener Conversación
            </button>
            
            <button
              className={`control-button control-button--mute ${state.isSoundMuted ? 'control-button--muted' : ''}`}
              onClick={handleToggleMute}
            >
              {state.isSoundMuted ? '🔇 Activar Audio' : '🔊 Silenciar'}
            </button>
          </div>
        )}
      </div>

      {/* Mensajes de error */}
      {error && (
        <div className="fluid-conversation-interface__error">
          <div className="error-message">
            ❌ {error}
          </div>
        </div>
      )}

      {/* Información de estado */}
      <div className="fluid-conversation-interface__info">
        <div className="info-grid">
          <div className="info-item">
            <span className="info-label">Estado Audio:</span>
            <span className="info-value">{state.audioState}</span>
          </div>
          <div className="info-item">
            <span className="info-label">Permisos:</span>
            <span className="info-value">
              {state.microphoneState.isPermissionGranted ? '✅ Concedidos' : '❌ Denegados'}
            </span>
          </div>
          <div className="info-item">
            <span className="info-label">Nivel Mic:</span>
            <span className="info-value">{state.microphoneState.level}%</span>
          </div>
        </div>
      </div>
    </div>
  );
};
