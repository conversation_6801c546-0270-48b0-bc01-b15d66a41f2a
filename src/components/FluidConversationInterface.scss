/**
 * Estilos para la interfaz de conversación fluida
 */

.fluid-conversation-interface {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  max-width: 600px;
  margin: 0 auto;

  // ========== HEADER ==========
  &__header {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__not-available {
    text-align: center;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
  }

  // ========== ESTADO DE CONEXIÓN ==========
  .connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;

    &__indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    &--connected {
      background: rgba(34, 197, 94, 0.2);
      border: 1px solid rgba(34, 197, 94, 0.3);
      
      .connection-status__indicator {
        background: #22c55e;
      }
    }

    &--connecting,
    &--reconnecting {
      background: rgba(251, 191, 36, 0.2);
      border: 1px solid rgba(251, 191, 36, 0.3);
      
      .connection-status__indicator {
        background: #fbbf24;
      }
    }

    &--disconnected,
    &--error {
      background: rgba(239, 68, 68, 0.2);
      border: 1px solid rgba(239, 68, 68, 0.3);
      
      .connection-status__indicator {
        background: #ef4444;
      }
    }
  }

  // ========== ÁREA DE TRANSCRIPCIÓN ==========
  &__transcription {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .transcription-area {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    &__current,
    &__reply {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      label {
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .transcription-text,
    .reply-text {
      background: rgba(0, 0, 0, 0.3);
      padding: 0.75rem;
      border-radius: 6px;
      min-height: 2.5rem;
      display: flex;
      align-items: center;
      font-size: 0.875rem;
      line-height: 1.4;
      border-left: 3px solid #3b82f6;
    }

    .reply-text {
      border-left-color: #10b981;
      background: rgba(16, 185, 129, 0.1);
    }
  }

  // ========== INDICADOR DE MICRÓFONO ==========
  &__microphone {
    display: flex;
    justify-content: center;
  }

  .microphone-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    min-width: 120px;

    &--active {
      border-color: #3b82f6;
      background: rgba(59, 130, 246, 0.1);
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);

      .microphone-icon {
        animation: pulse 1.5s infinite;
      }
    }

    .microphone-icon {
      font-size: 2rem;
      filter: grayscale(1);
      transition: filter 0.3s ease;
    }

    &--active .microphone-icon {
      filter: grayscale(0);
    }

    .microphone-level {
      width: 80px;
      height: 4px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;

      &__bar {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
        border-radius: 2px;
        transition: width 0.1s ease;
      }
    }

    .microphone-status {
      font-size: 0.75rem;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  // ========== ESTADO DE LA IA ==========
  &__ai-status {
    display: flex;
    justify-content: center;
    min-height: 2rem;
  }

  .ai-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;

    &--loading {
      background: rgba(251, 191, 36, 0.2);
      border: 1px solid rgba(251, 191, 36, 0.3);
      color: #fbbf24;

      .ai-status__spinner {
        animation: spin 1s linear infinite;
      }
    }

    &--speaking {
      background: rgba(16, 185, 129, 0.2);
      border: 1px solid rgba(16, 185, 129, 0.3);
      color: #10b981;

      .ai-status__icon {
        animation: pulse 1s infinite;
      }
    }
  }

  // ========== CONTROLES ==========
  &__controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  .control-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &--start {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #2563eb, #1e40af);
        transform: translateY(-1px);
      }
    }

    &--stop {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;

      &:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        transform: translateY(-1px);
      }
    }

    &--mute {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &--muted {
        background: rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.3);
        color: #ef4444;
      }
    }
  }

  .control-buttons {
    display: flex;
    gap: 1rem;
  }

  // ========== MENSAJES DE ERROR ==========
  &__error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 1rem;

    .error-message {
      color: #fca5a5;
      font-size: 0.875rem;
      text-align: center;
    }
  }

  // ========== INFORMACIÓN DE ESTADO ==========
  &__info {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    .info-label {
      font-size: 0.75rem;
      color: rgba(255, 255, 255, 0.6);
      font-weight: 500;
    }

    .info-value {
      font-size: 0.875rem;
      color: white;
      font-weight: 600;
    }
  }
}

// ========== ANIMACIONES ==========
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// ========== RESPONSIVE ==========
@media (max-width: 768px) {
  .fluid-conversation-interface {
    padding: 1rem;
    gap: 1rem;

    .control-buttons {
      flex-direction: column;
      align-items: center;
    }

    .control-button {
      min-width: 200px;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }
  }
}
