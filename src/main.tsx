import { createRoot } from "react-dom/client";
import { StrictMode } from "react";
import { AppProvider } from "./contexts/AppContext.tsx";
import { EventBusProvider } from "./contexts/EventBusContext.tsx";
import { SpeechProvider } from "./contexts/SpeechProvider.tsx";
import { FluidConversationProvider } from "./contexts/FluidConversationContext.tsx";
import { EnygmaGameProvider } from "./contexts/EnygmaGameContext.tsx";
import { GameOrchestratorProvider } from "./contexts/GameOrchestratorContext.tsx";
import App from "./App.tsx";
import "./index.scss";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AppProvider>
      <EventBusProvider>
        <SpeechProvider>
          <FluidConversationProvider>
            <EnygmaGameProvider>
              <GameOrchestratorProvider>
                <App />
              </GameOrchestratorProvider>
            </EnygmaGameProvider>
          </FluidConversationProvider>
        </SpeechProvider>
      </EventBusProvider>
    </AppProvider>
  </StrictMode>,
);
